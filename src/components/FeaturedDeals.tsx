
import { FeaturedDealCard } from "@/components/sale/FeaturedDealCard";

export const FeaturedDeals = () => {
  // Conversion rate: 1 USD = 134 NPR (approximate)
  const usdToNpr = 134;
  const convertToNpr = (usdPrice: number) => Math.round(usdPrice * usdToNpr);

  const deals = [
    {
      title: "Designer Blazers",
      description: "Professional blazers perfect for the office or special occasions. Premium tailoring at incredible prices.",
      discount: "24% Off",
      originalPrice: convertToNpr(249),
      salePrice: convertToNpr(189)
    },
    {
      title: "Leather Jackets", 
      description: "Genuine leather jackets that never go out of style. Investment pieces that improve with age.",
      discount: "25% Off",
      originalPrice: convertToNpr(399),
      salePrice: convertToNpr(299)
    },
    {
      title: "Premium Dresses",
      description: "Elegant dresses for every occasion. From cocktail parties to business meetings.",
      discount: "28% Off", 
      originalPrice: convertToNpr(179),
      salePrice: convertToNpr(129)
    }
  ];

  return (
    <section className="py-12 sm:py-16 bg-red-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl font-light text-neutral-900 mb-4">
            Featured Deals
          </h2>
          <p className="text-neutral-600 max-w-2xl mx-auto px-4">
            Our biggest discounts on the most sought-after pieces
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {deals.map((deal, index) => (
            <FeaturedDealCard
              key={index}
              title={deal.title}
              description={deal.description}
              discount={deal.discount}
              originalPrice={deal.originalPrice}
              salePrice={deal.salePrice}
            />
          ))}
        </div>
      </div>
    </section>
  );
};
