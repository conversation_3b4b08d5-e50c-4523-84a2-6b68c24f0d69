
import { Heart, Leaf, Award } from "lucide-react";

export const BrandStorySection = () => {
  const values = [
    {
      icon: Heart,
      title: "Crafted with Care",
      description: "Every piece is thoughtfully designed with attention to detail and quality craftsmanship."
    },
    {
      icon: Leaf,
      title: "Sustainable Fashion",
      description: "We're committed to responsible fashion through sustainable materials and ethical production."
    },
    {
      icon: Award,
      title: "Timeless Quality",
      description: "Our pieces are designed to last, creating a wardrobe that transcends seasonal trends."
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Story Content */}
          <div>
            <h2 className="text-4xl font-light text-neutral-900 mb-6">
              Our Story
            </h2>
            <p className="text-lg text-neutral-600 mb-6 leading-relaxed">
              Founded with a vision to redefine contemporary fashion, we believe that style should be effortless, sustainable, and accessible. Our journey began with a simple idea: create pieces that make women feel confident and comfortable in their own skin.
            </p>
            <p className="text-neutral-600 mb-8 leading-relaxed">
              Each collection is carefully curated to offer versatile pieces that seamlessly transition from day to night, work to weekend. We partner with skilled artisans and use premium, responsibly-sourced materials to ensure every piece meets our standards of excellence.
            </p>
            
            {/* Values */}
            <div className="space-y-6">
              {values.map((value, index) => (
                <div 
                  key={value.title}
                  className="flex items-start animate-fade-in"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="bg-neutral-100 p-3 rounded-lg mr-4 flex-shrink-0">
                    <value.icon className="h-6 w-6 text-neutral-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-neutral-900 mb-2">{value.title}</h3>
                    <p className="text-neutral-600 text-sm">{value.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Story Image */}
          <div className="relative">
            <div className="aspect-square rounded-lg overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1556905055-8f358a7a47b2?w=800"
                alt="Our story"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="absolute -bottom-6 -left-6 bg-white p-6 rounded-lg shadow-lg">
              <p className="text-sm text-neutral-500 mb-1">Established</p>
              <p className="text-2xl font-light text-neutral-900">2020</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
