
import React from 'react';
import { MapPin, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const Map = () => {
  const storeAddress = "123 Fashion Avenue, New York, NY 10001";
  const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(storeAddress)}`;

  return (
    <div className="bg-neutral-100 rounded-lg overflow-hidden shadow-lg">
      {/* Store Location Info */}
      <div className="p-6 bg-white">
        <div className="flex items-center space-x-3 mb-4">
          <MapPin className="h-6 w-6 text-neutral-600" />
          <div>
            <h3 className="font-medium text-neutral-900">Ziah Store</h3>
            <p className="text-neutral-600">{storeAddress}</p>
          </div>
        </div>
        
        <div className="space-y-3">
          <Button 
            onClick={() => window.open(googleMapsUrl, '_blank')}
            className="w-full bg-neutral-900 hover:bg-neutral-800"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Open in Google Maps
          </Button>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-medium text-neutral-900">Neighborhood</p>
              <p className="text-neutral-600">Fashion District</p>
            </div>
            <div>
              <p className="font-medium text-neutral-900">Nearest Subway</p>
              <p className="text-neutral-600">Herald Square</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Map placeholder with directions */}
      <div className="h-64 bg-gradient-to-br from-neutral-200 to-neutral-300 flex items-center justify-center">
        <div className="text-center text-neutral-600">
          <MapPin className="h-12 w-12 mx-auto mb-3 text-neutral-400" />
          <p className="font-medium mb-2">Store Location</p>
          <p className="text-sm">Manhattan Fashion District</p>
          <p className="text-sm">New York, NY</p>
        </div>
      </div>
    </div>
  );
};

export default Map;
