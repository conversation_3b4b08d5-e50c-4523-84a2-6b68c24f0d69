
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

export const HeroSection = () => {
  const navigate = useNavigate();

  const handleShopCollection = () => {
    navigate('/new-arrivals');
  };

  const handleExploreLookbook = () => {
    navigate('/women');
  };

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('/lovable-uploads/aa0dcea2-83c3-4a78-babd-0e7e09db5cf7.png')`,
        }}
      >
        <div className="absolute inset-0 bg-black/30"></div>
      </div>
      
      {/* Content */}
      <div className="relative z-10 text-center text-white max-w-4xl mx-auto px-4">
        <h1 className="text-5xl md:text-7xl font-light mb-6 tracking-wide animate-fade-in">
          Elevate Your Style
        </h1>
        <p className="text-xl md:text-2xl mb-8 font-light opacity-90 animate-fade-in delay-200">
          Discover the latest trends in contemporary fashion
        </p>
        <div className="space-x-4 animate-fade-in delay-400">
          <Button 
            size="lg" 
            className="bg-white text-black hover:bg-neutral-100 px-8 py-3 text-lg font-medium"
            onClick={handleShopCollection}
          >
            Shop Collection
          </Button>
          <Button 
            variant="outline" 
            size="lg" 
            className="border-white text-white hover:bg-white hover:text-black px-8 py-3 text-lg font-medium"
            onClick={handleExploreLookbook}
          >
            Explore Lookbook
          </Button>
        </div>
      </div>
    </section>
  );
};
