
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";

export const Navigation = () => {
  const location = useLocation();

  const navItems = [
    { name: "New Arrivals", href: "/new-arrivals" },
    { name: "Women", href: "/women" },
    { name: "Men", href: "/men" },
    { name: "Accessories", href: "/accessories" },
    { name: "Sale", href: "/sale" },
  ];

  return (
    <nav className="hidden md:block">
      <ul className="flex space-x-8">
        {navItems.map((item) => (
          <li key={item.name}>
            <Link
              to={item.href}
              className={`text-sm font-medium transition-colors hover:text-neutral-600 ${
                location.pathname === item.href ? "text-neutral-900" : "text-neutral-700"
              }`}
            >
              {item.name}
            </Link>
          </li>
        ))}
      </ul>
    </nav>
  );
};
