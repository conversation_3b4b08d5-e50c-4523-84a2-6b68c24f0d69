
import { Button } from "@/components/ui/button";

export const FeaturedSection = () => {
  const collections = [
    {
      title: "Summer Essentials",
      description: "Light, breathable fabrics for the season",
      image: "/lovable-uploads/e59963c8-f33d-4397-9c85-f93c9c1f382b.png",
      cta: "Shop Summer"
    },
    {
      title: "Professional Wear",
      description: "Sophisticated pieces for the modern workplace",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800",
      cta: "Shop Professional"
    },
    {
      title: "Weekend Casual",
      description: "Comfortable yet stylish everyday wear",
      image: "https://images.unsplash.com/photo-1649972904349-6e44c42644a7?w=800",
      cta: "Shop Casual"
    }
  ];

  return (
    <section className="py-20 bg-neutral-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-light text-neutral-900 mb-4">
            Featured Collections
          </h2>
          <p className="text-neutral-600 text-lg max-w-2xl mx-auto">
            Curated selections that define contemporary style and comfort
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {collections.map((collection, index) => (
            <div 
              key={collection.title}
              className="group cursor-pointer animate-fade-in"
              style={{ animationDelay: `${index * 150}ms` }}
            >
              <div className="relative overflow-hidden rounded-lg mb-4 aspect-square">
                <img
                  src={collection.image}
                  alt={collection.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors duration-300"></div>
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <Button 
                    className="bg-white text-black hover:bg-neutral-100 transform scale-95 group-hover:scale-100 transition-transform duration-300"
                  >
                    {collection.cta}
                  </Button>
                </div>
              </div>
              <h3 className="text-xl font-medium text-neutral-900 mb-2">
                {collection.title}
              </h3>
              <p className="text-neutral-600">
                {collection.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
