
import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Navigation } from "@/components/Navigation";
import { Footer } from "@/components/Footer";

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout = ({ children }: LayoutProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  // Scroll to top when route changes
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-white flex flex-col relative prevent-layout-shift">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-neutral-200 shadow-sm transition-all duration-300">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center min-w-0 flex-1 md:flex-none">
              <Link to="/" className="transition-opacity hover:opacity-80">
                <h1 className="text-xl sm:text-2xl font-bold tracking-wide text-neutral-900">
                  Ziah
                </h1>
              </Link>
            </div>

            {/* Desktop Navigation - Centered */}
            <div className="hidden md:flex flex-1 justify-center">
              <Navigation />
            </div>

            {/* Contact Us & Mobile Menu */}
            <div className="flex items-center min-w-0 flex-1 md:flex-none justify-end">
              {/* Desktop Contact Us */}
              <Link
                to="/contact"
                className={`hidden md:block text-sm font-medium transition-all duration-200 hover:text-neutral-900 relative group mr-4 ${
                  location.pathname === "/contact" ? "text-neutral-900" : "text-neutral-700"
                }`}
              >
                Contact Us
                <span className={`absolute -bottom-1 left-0 w-0 h-0.5 bg-neutral-900 transition-all duration-200 group-hover:w-full ${
                  location.pathname === "/contact" ? "w-full" : ""
                }`}></span>
              </Link>

              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden hover:bg-neutral-100 transition-colors"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden py-6 border-t border-neutral-200 bg-white/95 backdrop-blur-sm">
              <nav className="space-y-4 px-2">
                <Link
                  to="/new-arrivals"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  New Arrivals
                </Link>
                <Link
                  to="/women"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Women
                </Link>
                <Link
                  to="/men"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Men
                </Link>
                <Link
                  to="/accessories"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Accessories
                </Link>
                <Link
                  to="/sale"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Sale
                </Link>
                <Link
                  to="/contact"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Contact Us
                </Link>
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 relative z-10 page-transition">
        <div className="min-h-[calc(100vh-4rem)] prevent-layout-shift">
          {children}
        </div>
      </main>

      <Footer />
    </div>
  );
};
