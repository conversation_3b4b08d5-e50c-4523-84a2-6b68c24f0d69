
import { useState } from "react";
import { Menu, X } from "lucide-react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Navigation } from "@/components/Navigation";
import { Footer } from "@/components/Footer";

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout = ({ children }: LayoutProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-neutral-200 shadow-sm">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link to="/" className="transition-opacity hover:opacity-80">
                <h1 className="text-xl sm:text-2xl font-bold tracking-wide text-neutral-900">
                  Ziah
                </h1>
              </Link>
            </div>

            {/* Desktop Navigation - Centered */}
            <div className="hidden md:flex flex-1 justify-center">
              <Navigation />
            </div>

            {/* Mobile Menu Button */}
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden hover:bg-neutral-100 transition-colors"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation Menu */}
          {isMobileMenuOpen && (
            <div className="md:hidden py-6 border-t border-neutral-200 bg-white/95 backdrop-blur-sm">
              <nav className="space-y-4 px-2">
                <Link
                  to="/new-arrivals"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  New Arrivals
                </Link>
                <Link
                  to="/women"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Women
                </Link>
                <Link
                  to="/men"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Men
                </Link>
                <Link
                  to="/accessories"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Accessories
                </Link>
                <Link
                  to="/sale"
                  className="block py-2 px-3 text-neutral-700 hover:text-neutral-900 hover:bg-neutral-50 rounded-md transition-all duration-200"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Sale
                </Link>
              </nav>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main>
        {children}
      </main>

      <Footer />
    </div>
  );
};
