import { Layout } from "@/components/Layout";
import { ProductList } from "@/components/ProductList";
import { Badge } from "@/components/ui/badge";
import { Crown, Heart, Star } from "lucide-react";
import { useProducts } from "@/hooks/useProducts";

const Women = () => {
  const { products, loading } = useProducts({ gender: "women" });

  if (loading) {
    return (
      <Layout>
        <div className="py-20 text-center">
          <p>Loading women's collection...</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-rose-50 to-neutral-50 py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-6">
            <Crown className="h-8 w-8 text-rose-600 mr-3" />
            <Badge className="bg-rose-600 text-white">Women's Collection</Badge>
          </div>
          <h1 className="text-5xl font-light text-neutral-900 mb-6">
            Sophisticated Elegance
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-8">
            Discover pieces designed for the modern woman who values both style and substance. Our women's collection celebrates femininity through timeless silhouettes and contemporary details.
          </p>
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto mt-12">
            <div className="text-center">
              <Heart className="h-8 w-8 text-rose-600 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-900 mb-2">Feminine Details</h3>
              <p className="text-sm text-neutral-600">Thoughtful touches that enhance natural beauty</p>
            </div>
            <div className="text-center">
              <Star className="h-8 w-8 text-rose-600 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-900 mb-2">Premium Quality</h3>
              <p className="text-sm text-neutral-600">Luxurious fabrics and impeccable craftsmanship</p>
            </div>
            <div className="text-center">
              <Crown className="h-8 w-8 text-rose-600 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-900 mb-2">Versatile Pieces</h3>
              <p className="text-sm text-neutral-600">From boardroom to brunch, effortlessly</p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-light text-neutral-900 mb-4">
              Shop by Category
            </h2>
            <p className="text-neutral-600 max-w-2xl mx-auto">
              Curated collections for every aspect of your lifestyle
            </p>
          </div>
          
          <div className="grid md:grid-cols-4 gap-6">
            <div className="bg-neutral-50 p-6 rounded-lg text-center hover:bg-neutral-100 transition-colors cursor-pointer">
              <h3 className="font-medium text-neutral-900 mb-2">Workwear</h3>
              <p className="text-sm text-neutral-600">Professional pieces for the career woman</p>
            </div>
            <div className="bg-neutral-50 p-6 rounded-lg text-center hover:bg-neutral-100 transition-colors cursor-pointer">
              <h3 className="font-medium text-neutral-900 mb-2">Evening</h3>
              <p className="text-sm text-neutral-600">Elegant options for special occasions</p>
            </div>
            <div className="bg-neutral-50 p-6 rounded-lg text-center hover:bg-neutral-100 transition-colors cursor-pointer">
              <h3 className="font-medium text-neutral-900 mb-2">Casual</h3>
              <p className="text-sm text-neutral-600">Comfortable yet stylish everyday wear</p>
            </div>
            <div className="bg-neutral-50 p-6 rounded-lg text-center hover:bg-neutral-100 transition-colors cursor-pointer">
              <h3 className="font-medium text-neutral-900 mb-2">Seasonal</h3>
              <p className="text-sm text-neutral-600">Weather-appropriate fashion essentials</p>
            </div>
          </div>
        </div>
      </section>

      <ProductList 
        products={products}
        title="Women's Collection"
        description="Sophisticated pieces designed for the modern woman"
      />

      {/* Styling Tips */}
      <section className="py-16 bg-rose-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-light text-neutral-900 mb-4">
              Styling Inspiration
            </h2>
            <p className="text-neutral-600 max-w-2xl mx-auto">
              Expert tips on how to create stunning looks with our women's pieces
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg">
              <h3 className="text-lg font-medium text-neutral-900 mb-3">Power Dressing</h3>
              <p className="text-neutral-600 mb-4">
                Command attention in boardroom meetings with our tailored blazers and high-waist trousers. Add a silk blouse for a touch of feminine sophistication.
              </p>
              <p className="text-sm text-neutral-500">Perfect for: Business meetings, presentations, networking events</p>
            </div>
            <div className="bg-white p-6 rounded-lg">
              <h3 className="text-lg font-medium text-neutral-900 mb-3">Effortless Elegance</h3>
              <p className="text-neutral-600 mb-4">
                Create timeless looks with our elegant dresses and cashmere sweaters. These pieces transition beautifully from day to evening with simple accessory changes.
              </p>
              <p className="text-sm text-neutral-500">Perfect for: Dinner dates, cultural events, weekend brunches</p>
            </div>
            <div className="bg-white p-6 rounded-lg">
              <h3 className="text-lg font-medium text-neutral-900 mb-3">Seasonal Layering</h3>
              <p className="text-neutral-600 mb-4">
                Master the art of layering with our wool coats and versatile pieces. Build sophisticated outfits that adapt to changing weather and occasions.
              </p>
              <p className="text-sm text-neutral-500">Perfect for: Transitional weather, travel, outdoor events</p>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Women;
