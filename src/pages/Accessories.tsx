import { Layout } from "@/components/Layout";
import { ProductList } from "@/components/ProductList";
import { Badge } from "@/components/ui/badge";
import { Gem, Watch, Briefcase, Sparkles } from "lucide-react";
import { useProducts } from "@/hooks/useProducts";

const Accessories = () => {
  const { products, loading } = useProducts({ collection: "accessories" });

  if (loading) {
    return (
      <Layout>
        <div className="py-20 text-center">
          <p>Loading accessories...</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-amber-50 to-neutral-50 py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-6">
            <Gem className="h-8 w-8 text-amber-600 mr-3" />
            <Badge className="bg-amber-600 text-white">Accessories Collection</Badge>
          </div>
          <h1 className="text-5xl font-light text-neutral-900 mb-6">
            Finishing Touches
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-8">
            Complete your look with our curated selection of accessories. From statement jewelry to functional leather goods, each piece adds personality and polish to your ensemble.
          </p>
          <div className="grid md:grid-cols-4 gap-6 max-w-5xl mx-auto mt-12">
            <div className="text-center">
              <Gem className="h-8 w-8 text-amber-600 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-900 mb-2">Jewelry</h3>
              <p className="text-sm text-neutral-600">Elegant pieces for every occasion</p>
            </div>
            <div className="text-center">
              <Briefcase className="h-8 w-8 text-amber-600 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-900 mb-2">Leather Goods</h3>
              <p className="text-sm text-neutral-600">Handcrafted bags and accessories</p>
            </div>
            <div className="text-center">
              <Watch className="h-8 w-8 text-amber-600 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-900 mb-2">Timepieces</h3>
              <p className="text-sm text-neutral-600">Classic and contemporary watches</p>
            </div>
            <div className="text-center">
              <Sparkles className="h-8 w-8 text-amber-600 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-900 mb-2">Special Details</h3>
              <p className="text-sm text-neutral-600">Unique touches that make a statement</p>
            </div>
          </div>
        </div>
      </section>

      {/* Categories Showcase */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-light text-neutral-900 mb-4">
              Accessory Categories
            </h2>
            <p className="text-neutral-600 max-w-2xl mx-auto">
              Discover the perfect pieces to complement your personal style
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="group cursor-pointer">
              <div className="bg-amber-50 p-8 rounded-lg text-center hover:bg-amber-100 transition-colors">
                <Gem className="h-12 w-12 text-amber-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-neutral-900 mb-2">Fine Jewelry</h3>
                <p className="text-sm text-neutral-600 mb-4">Handpicked pieces featuring precious metals and stones</p>
                <ul className="text-xs text-neutral-500 space-y-1">
                  <li>• Necklaces & Pendants</li>
                  <li>• Earrings & Studs</li>
                  <li>• Bracelets & Bangles</li>
                  <li>• Rings & Statement Pieces</li>
                </ul>
              </div>
            </div>
            <div className="group cursor-pointer">
              <div className="bg-amber-50 p-8 rounded-lg text-center hover:bg-amber-100 transition-colors">
                <Briefcase className="h-12 w-12 text-amber-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-neutral-900 mb-2">Leather Goods</h3>
                <p className="text-sm text-neutral-600 mb-4">Premium leather accessories crafted to last</p>
                <ul className="text-xs text-neutral-500 space-y-1">
                  <li>• Handbags & Totes</li>
                  <li>• Crossbody & Clutches</li>
                  <li>• Belts & Buckles</li>
                  <li>• Wallets & Cardholders</li>
                </ul>
              </div>
            </div>
            <div className="group cursor-pointer">
              <div className="bg-amber-50 p-8 rounded-lg text-center hover:bg-amber-100 transition-colors">
                <Watch className="h-12 w-12 text-amber-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-neutral-900 mb-2">Timepieces</h3>
                <p className="text-sm text-neutral-600 mb-4">Classic and modern watches for every wrist</p>
                <ul className="text-xs text-neutral-500 space-y-1">
                  <li>• Dress Watches</li>
                  <li>• Sport Chronographs</li>
                  <li>• Vintage Inspired</li>
                  <li>• Smart Timepieces</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <ProductList 
        products={products}
        title="Accessories Collection"
        description="Complete your look with our curated selection of accessories"
      />

      {/* Styling Guide */}
      <section className="py-16 bg-amber-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-light text-neutral-900 mb-4">
              Accessory Styling Guide
            </h2>
            <p className="text-neutral-600 max-w-2xl mx-auto">
              Learn how to style accessories to enhance your outfits
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h3 className="text-xl font-medium text-neutral-900 mb-4">The Rule of Three</h3>
              <p className="text-neutral-600 mb-4">
                When accessorizing, limit yourself to three key pieces to avoid overwhelming your look. For example, pair a watch, a necklace, and a handbag for a balanced ensemble.
              </p>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>• Choose one statement piece as your focal point</li>
                <li>• Keep other accessories subtle and complementary</li>
                <li>• Consider the occasion and dress code</li>
                <li>• Mix textures and materials thoughtfully</li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-medium text-neutral-900 mb-4">Color Coordination</h3>
              <p className="text-neutral-600 mb-4">
                Coordinate your accessories with your outfit's color palette. Metallic tones should be consistent, and leather goods should complement your shoes when possible.
              </p>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>• Match metal tones across all jewelry pieces</li>
                <li>• Coordinate leather accessories with footwear</li>
                <li>• Use scarves to tie color schemes together</li>
                <li>• Consider seasonal color palettes</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Care & Maintenance */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-light text-neutral-900 mb-4">
              Care & Maintenance
            </h2>
            <p className="text-neutral-600 max-w-2xl mx-auto">
              Proper care ensures your accessories maintain their beauty and value
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-neutral-100 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
                <Gem className="h-8 w-8 text-neutral-600" />
              </div>
              <h3 className="font-medium text-neutral-900 mb-3">Jewelry Care</h3>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>• Store pieces separately to prevent scratching</li>
                <li>• Clean with appropriate jewelry cleaners</li>
                <li>• Remove before swimming or showering</li>
                <li>• Professional cleaning annually</li>
              </ul>
            </div>
            <div className="text-center">
              <div className="bg-neutral-100 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
                <Briefcase className="h-8 w-8 text-neutral-600" />
              </div>
              <h3 className="font-medium text-neutral-900 mb-3">Leather Care</h3>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>• Condition leather regularly with quality products</li>
                <li>• Store in dust bags when not in use</li>
                <li>• Avoid direct sunlight and extreme temperatures</li>
                <li>• Professional cleaning for stubborn stains</li>
              </ul>
            </div>
            <div className="text-center">
              <div className="bg-neutral-100 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
                <Watch className="h-8 w-8 text-neutral-600" />
              </div>
              <h3 className="font-medium text-neutral-900 mb-3">Watch Care</h3>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>• Service mechanical watches every 3-5 years</li>
                <li>• Keep away from magnets and extreme temperatures</li>
                <li>• Clean with soft, damp cloth regularly</li>
                <li>• Check water resistance before exposure</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Accessories;
