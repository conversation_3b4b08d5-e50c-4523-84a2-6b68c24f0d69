import { Layout } from "@/components/Layout";
import { ProductList } from "@/components/ProductList";
import { Badge } from "@/components/ui/badge";
import { Shield, Target, Briefcase } from "lucide-react";
import { useProducts } from "@/hooks/useProducts";

const Men = () => {
  const { products, loading } = useProducts({ gender: "men" });

  if (loading) {
    return (
      <Layout>
        <div className="py-20 text-center">
          <p>Loading men's collection...</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-slate-50 to-neutral-50 py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-6">
            <Shield className="h-8 w-8 text-slate-600 mr-3" />
            <Badge className="bg-slate-600 text-white">Men's Collection</Badge>
          </div>
          <h1 className="text-5xl font-light text-neutral-900 mb-6">
            Contemporary Menswear
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-8">
            For the discerning gentleman who appreciates quality, craftsmanship, and timeless style. Our men's collection features modern cuts with classic sensibilities.
          </p>
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto mt-12">
            <div className="text-center">
              <Briefcase className="h-8 w-8 text-slate-600 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-900 mb-2">Professional</h3>
              <p className="text-sm text-neutral-600">Elevate your work wardrobe</p>
            </div>
            <div className="text-center">
              <Target className="h-8 w-8 text-slate-600 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-900 mb-2">Precision Fit</h3>
              <p className="text-sm text-neutral-600">Tailored for the modern man</p>
            </div>
            <div className="text-center">
              <Shield className="h-8 w-8 text-slate-600 mx-auto mb-3" />
              <h3 className="font-medium text-neutral-900 mb-2">Durable Quality</h3>
              <p className="text-sm text-neutral-600">Built to last, styled to impress</p>
            </div>
          </div>
        </div>
      </section>

      {/* Essential Categories */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-light text-neutral-900 mb-4">
              Essential Categories
            </h2>
            <p className="text-neutral-600 max-w-2xl mx-auto">
              Build a comprehensive wardrobe with our carefully curated categories
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-slate-50 p-6 rounded-lg text-center hover:bg-slate-100 transition-colors cursor-pointer">
              <h3 className="font-medium text-neutral-900 mb-2">Suiting</h3>
              <p className="text-sm text-neutral-600">Classic and modern suit separates</p>
            </div>
            <div className="bg-slate-50 p-6 rounded-lg text-center hover:bg-slate-100 transition-colors cursor-pointer">
              <h3 className="font-medium text-neutral-900 mb-2">Casual</h3>
              <p className="text-sm text-neutral-600">Relaxed styles for weekend wear</p>
            </div>
            <div className="bg-slate-50 p-6 rounded-lg text-center hover:bg-slate-100 transition-colors cursor-pointer">
              <h3 className="font-medium text-neutral-900 mb-2">Knitwear</h3>
              <p className="text-sm text-neutral-600">Premium sweaters and cardigans</p>
            </div>
            <div className="bg-slate-50 p-6 rounded-lg text-center hover:bg-slate-100 transition-colors cursor-pointer">
              <h3 className="font-medium text-neutral-900 mb-2">Outerwear</h3>
              <p className="text-sm text-neutral-600">Jackets and coats for all seasons</p>
            </div>
          </div>
        </div>
      </section>

      <ProductList 
        products={products}
        title="Men's Collection"
        description="Contemporary menswear for the discerning gentleman"
      />

      {/* Fit Guide */}
      <section className="py-16 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-light text-neutral-900 mb-4">
              The Perfect Fit
            </h2>
            <p className="text-neutral-600 max-w-2xl mx-auto">
              Understanding fit is crucial to looking your best. Here's how our pieces should fit
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg">
              <h3 className="text-lg font-medium text-neutral-900 mb-3">Blazers & Suits</h3>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>• Shoulders should sit naturally without padding bunching</li>
                <li>• Sleeve length shows 1/4 to 1/2 inch of shirt cuff</li>
                <li>• Body should skim your torso without pulling</li>
                <li>• Jacket length covers your seat</li>
              </ul>
            </div>
            <div className="bg-white p-6 rounded-lg">
              <h3 className="text-lg font-medium text-neutral-900 mb-3">Dress Shirts</h3>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>• Collar should allow one finger between neck and fabric</li>
                <li>• Sleeve length hits wrist bone when arms hang naturally</li>
                <li>• Body provides room to move without excess fabric</li>
                <li>• Shoulder seams align with your shoulder points</li>
              </ul>
            </div>
            <div className="bg-white p-6 rounded-lg">
              <h3 className="text-lg font-medium text-neutral-900 mb-3">Trousers</h3>
              <ul className="text-sm text-neutral-600 space-y-2">
                <li>• Waist sits comfortably at natural waistline</li>
                <li>• Rise provides adequate coverage when seated</li>
                <li>• Leg opening allows for appropriate shoe pairing</li>
                <li>• Length creates slight break or no break on shoes</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Care Instructions */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-light text-neutral-900 mb-4">
              Care & Maintenance
            </h2>
            <p className="text-neutral-600 max-w-2xl mx-auto">
              Proper care ensures your investment pieces look great for years to come
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-12">
            <div>
              <h3 className="text-xl font-medium text-neutral-900 mb-4">General Care</h3>
              <ul className="text-neutral-600 space-y-3">
                <li>• Always check care labels before cleaning</li>
                <li>• Invest in quality hangers to maintain shape</li>
                <li>• Allow pieces to air out between wears</li>
                <li>• Store in breathable garment bags for long-term storage</li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-medium text-neutral-900 mb-4">Professional Cleaning</h3>
              <ul className="text-neutral-600 space-y-3">
                <li>• Suits and blazers benefit from professional dry cleaning</li>
                <li>• Clean suits only when necessary to preserve fabric</li>
                <li>• Find a trusted dry cleaner who specializes in menswear</li>
                <li>• Press shirts professionally for the crispest finish</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
};

export default Men;
